import { forwardRef } from 'react'
import { motion } from 'framer-motion'
import { clsx } from 'clsx'

const Card = forwardRef(({
  children,
  className,
  variant = 'default',
  padding = 'md',
  rounded = 'lg',
  shadow = 'md',
  hover = false,
  clickable = false,
  neumorphic = false,
  glass = false,
  gradient = false,
  onClick,
  ...props
}, ref) => {
  const baseClasses = 'transition-all duration-200'

  const variants = {
    default: 'bg-white border border-gray-200 dark:bg-gray-800 dark:border-gray-700',
    filled: 'bg-gray-50 border border-gray-100 dark:bg-gray-900 dark:border-gray-800',
    outlined: 'bg-transparent border-2 border-gray-300 dark:border-gray-600',
    elevated: 'bg-white dark:bg-gray-800 border-0',
  }

  const paddings = {
    none: '',
    xs: 'p-2',
    sm: 'p-3',
    md: 'p-4',
    lg: 'p-6',
    xl: 'p-8',
  }

  const roundedClasses = {
    none: '',
    sm: 'rounded-sm',
    md: 'rounded-md',
    lg: 'rounded-lg',
    xl: 'rounded-xl',
    '2xl': 'rounded-2xl',
    '3xl': 'rounded-3xl',
    full: 'rounded-full',
  }

  const shadows = {
    none: '',
    sm: 'shadow-sm',
    md: 'shadow-md',
    lg: 'shadow-lg',
    xl: 'shadow-xl',
    '2xl': 'shadow-2xl',
    soft: 'shadow-soft',
    medium: 'shadow-medium',
    strong: 'shadow-strong',
  }

  const hoverEffects = hover ? {
    default: 'hover:shadow-lg hover:-translate-y-1',
    filled: 'hover:bg-gray-100 dark:hover:bg-gray-800',
    outlined: 'hover:border-primary-300 dark:hover:border-primary-600',
    elevated: 'hover:shadow-xl hover:-translate-y-1',
  } : {}

  const clickableClasses = clickable ? 'cursor-pointer select-none' : ''

  const neumorphicClasses = neumorphic 
    ? 'shadow-neumorphic hover:shadow-neumorphic-inset bg-gray-100 dark:bg-gray-800'
    : ''

  const glassClasses = glass 
    ? 'backdrop-blur-md bg-white/70 dark:bg-gray-900/70 border border-white/20 dark:border-gray-700/20 shadow-glass'
    : ''

  const gradientClasses = gradient 
    ? 'bg-gradient-to-br from-white to-gray-50 dark:from-gray-800 dark:to-gray-900'
    : ''

  const classes = clsx(
    baseClasses,
    !neumorphic && !glass && !gradient && variants[variant],
    paddings[padding],
    roundedClasses[rounded],
    !neumorphic && !glass && shadows[shadow],
    hover && hoverEffects[variant],
    clickableClasses,
    neumorphic && neumorphicClasses,
    glass && glassClasses,
    gradient && gradientClasses,
    className
  )

  const Component = clickable || onClick ? motion.div : 'div'
  const motionProps = clickable || onClick ? {
    whileHover: { scale: 1.02 },
    whileTap: { scale: 0.98 },
    transition: { type: "spring", stiffness: 400, damping: 17 }
  } : {}

  return (
    <Component
      ref={ref}
      className={classes}
      onClick={onClick}
      {...motionProps}
      {...props}
    >
      {children}
    </Component>
  )
})

Card.displayName = 'Card'

// Card sub-components
const CardHeader = ({ children, className, ...props }) => (
  <div className={clsx('mb-4', className)} {...props}>
    {children}
  </div>
)

const CardTitle = ({ children, className, as: Component = 'h3', ...props }) => (
  <Component 
    className={clsx('text-lg font-semibold text-gray-900 dark:text-gray-100', className)} 
    {...props}
  >
    {children}
  </Component>
)

const CardDescription = ({ children, className, ...props }) => (
  <p className={clsx('text-sm text-gray-600 dark:text-gray-400', className)} {...props}>
    {children}
  </p>
)

const CardContent = ({ children, className, ...props }) => (
  <div className={clsx('', className)} {...props}>
    {children}
  </div>
)

const CardFooter = ({ children, className, ...props }) => (
  <div className={clsx('mt-4 pt-4 border-t border-gray-200 dark:border-gray-700', className)} {...props}>
    {children}
  </div>
)

Card.Header = CardHeader
Card.Title = CardTitle
Card.Description = CardDescription
Card.Content = CardContent
Card.Footer = CardFooter

export default Card
