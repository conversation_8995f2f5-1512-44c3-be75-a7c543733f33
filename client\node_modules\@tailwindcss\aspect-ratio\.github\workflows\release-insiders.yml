name: Release Insiders

on:
  push:
    branches: [master]

jobs:
  build:
    runs-on: ubuntu-latest

    strategy:
      matrix:
        node-version: [12]

    steps:
      - uses: actions/checkout@v2

      - name: Use Node.js ${{ matrix.node-version }}
        uses: actions/setup-node@v2
        with:
          node-version: ${{ matrix.node-version }}
          registry-url: 'https://registry.npmjs.org'

      - name: Use cached node_modules
        id: cache
        uses: actions/cache@v2
        with:
          path: node_modules
          key: nodeModules-${{ hashFiles('**/package-lock.json') }}-${{ matrix.node-version }}
          restore-keys: |
            nodeModules-

      - name: Install dependencies
        if: steps.cache.outputs.cache-hit != 'true'
        run: npm install
        env:
          CI: true

      - name: Resolve version
        id: vars
        run: echo "::set-output name=sha_short::$(git rev-parse --short HEAD)"

      - name: "Version based on commit: 0.0.0-insiders.${{ steps.vars.outputs.sha_short }}"
        run: npm version 0.0.0-insiders.${{ steps.vars.outputs.sha_short }} --force --no-git-tag-version

      - name: Publish
        run: npm publish --tag insiders
        env:
          CI: true
          NODE_AUTH_TOKEN: ${{ secrets.NPM_TOKEN }}
