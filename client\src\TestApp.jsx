import React from 'react'

function TestApp() {
  return (
    <div className="min-h-screen bg-gray-100 flex items-center justify-center">
      <div className="text-center">
        <h1 className="text-4xl font-bold text-gray-900 mb-4">
          🏠 StayFinder
        </h1>
        <p className="text-lg text-gray-600 mb-8">
          React is working! The application is loading successfully.
        </p>
        <div className="bg-white p-6 rounded-lg shadow-lg max-w-md mx-auto">
          <h2 className="text-xl font-semibold mb-4">Next Steps:</h2>
          <ol className="text-left space-y-2 text-sm">
            <li>1. ✅ React app is running</li>
            <li>2. ✅ Tailwind CSS is working</li>
            <li>3. 🔧 Set up Supabase environment variables</li>
            <li>4. 🚀 Enable full application features</li>
          </ol>
        </div>
      </div>
    </div>
  )
}

export default TestApp
