import { Routes, Route, Navigate } from 'react-router-dom'
import { AnimatePresence } from 'framer-motion'
import { useAuth } from './context/SupabaseAuthContext'

// Layout Components
import Layout from './components/layout/Layout'
import ProtectedRoute from './components/auth/ProtectedRoute'

// Pages
import Home from './pages/Home'
import SearchResults from './pages/SearchResults'
import PropertyDetail from './pages/PropertyDetail'
import Login from './pages/auth/Login'
import Register from './pages/auth/Register'
import ForgotPassword from './pages/auth/ForgotPassword'
import Profile from './pages/Profile'
import Dashboard from './pages/Dashboard'
import HostDashboard from './pages/host/HostDashboard'
import AddProperty from './pages/host/AddProperty'
import ManageProperties from './pages/host/ManageProperties'
import BookingManagement from './pages/host/BookingManagement'
import NotFound from './pages/NotFound'

// Loading component
import LoadingSpinner from './components/ui/LoadingSpinner'

function App() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">
          <h1 className="text-6xl font-bold text-gray-900 mb-4">
            🏠 StayFinder
          </h1>
          <p className="text-xl text-gray-600 mb-8">
            Modern Airbnb Clone - React App is Working!
          </p>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-4xl mx-auto">
            <div className="bg-white p-6 rounded-xl shadow-lg">
              <div className="text-3xl mb-4">✅</div>
              <h3 className="text-lg font-semibold mb-2">React</h3>
              <p className="text-gray-600">Application is running successfully</p>
            </div>

            <div className="bg-white p-6 rounded-xl shadow-lg">
              <div className="text-3xl mb-4">🎨</div>
              <h3 className="text-lg font-semibold mb-2">Tailwind CSS</h3>
              <p className="text-gray-600">Styling is working perfectly</p>
            </div>

            <div className="bg-white p-6 rounded-xl shadow-lg">
              <div className="text-3xl mb-4">🔧</div>
              <h3 className="text-lg font-semibold mb-2">Next Steps</h3>
              <p className="text-gray-600">Set up Supabase to enable full features</p>
            </div>
          </div>

          <div className="mt-12 bg-white p-8 rounded-xl shadow-lg max-w-2xl mx-auto">
            <h2 className="text-2xl font-bold mb-4">🚀 Ready to Continue?</h2>
            <p className="text-gray-600 mb-6">
              The application foundation is working perfectly. To enable the full StayFinder experience:
            </p>
            <ol className="text-left space-y-3 text-gray-700">
              <li className="flex items-center">
                <span className="bg-blue-100 text-blue-800 rounded-full w-6 h-6 flex items-center justify-center text-sm font-bold mr-3">1</span>
                Create a Supabase project at supabase.com
              </li>
              <li className="flex items-center">
                <span className="bg-blue-100 text-blue-800 rounded-full w-6 h-6 flex items-center justify-center text-sm font-bold mr-3">2</span>
                Add your Supabase URL and API key to client/.env
              </li>
              <li className="flex items-center">
                <span className="bg-blue-100 text-blue-800 rounded-full w-6 h-6 flex items-center justify-center text-sm font-bold mr-3">3</span>
                Run the SQL schema from client/docs/supabase-schema.sql
              </li>
              <li className="flex items-center">
                <span className="bg-blue-100 text-blue-800 rounded-full w-6 h-6 flex items-center justify-center text-sm font-bold mr-3">4</span>
                Restart the development server
              </li>
            </ol>
          </div>
        </div>
      </div>
    </div>
  )
}

export default App
