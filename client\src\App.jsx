import { Routes, Route, Navigate } from 'react-router-dom'
import { AnimatePresence } from 'framer-motion'
import { useAuth } from './context/SupabaseAuthContext'

// Layout Components
import Layout from './components/layout/Layout'
import ProtectedRoute from './components/auth/ProtectedRoute'

// Pages
import Home from './pages/Home'
import SearchResults from './pages/SearchResults'
import PropertyDetail from './pages/PropertyDetail'
import Login from './pages/auth/Login'
import Register from './pages/auth/Register'
import ForgotPassword from './pages/auth/ForgotPassword'
import Profile from './pages/Profile'
import Dashboard from './pages/Dashboard'
import HostDashboard from './pages/host/HostDashboard'
import AddProperty from './pages/host/AddProperty'
import ManageProperties from './pages/host/ManageProperties'
import BookingManagement from './pages/host/BookingManagement'
import NotFound from './pages/NotFound'

// Loading component
import LoadingSpinner from './components/ui/LoadingSpinner'

function App() {
  const { user, loading, isAuthenticated } = useAuth()

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900">
        <LoadingSpinner size="lg" />
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 transition-colors">
      <AnimatePresence mode="wait">
        <Routes>
          {/* Public Routes */}
          <Route path="/" element={<Layout />}>
            <Route index element={<Home />} />
            <Route path="search" element={<SearchResults />} />
            <Route path="property/:id" element={<PropertyDetail />} />

            {/* Auth Routes - redirect if already logged in */}
            <Route
              path="login"
              element={isAuthenticated ? <Navigate to="/dashboard" replace /> : <Login />}
            />
            <Route
              path="register"
              element={isAuthenticated ? <Navigate to="/dashboard" replace /> : <Register />}
            />
            <Route
              path="forgot-password"
              element={isAuthenticated ? <Navigate to="/dashboard" replace /> : <ForgotPassword />}
            />

            {/* Protected Routes */}
            <Route path="profile" element={
              <ProtectedRoute>
                <Profile />
              </ProtectedRoute>
            } />
            <Route path="dashboard" element={
              <ProtectedRoute>
                <Dashboard />
              </ProtectedRoute>
            } />

            {/* Host Routes */}
            <Route path="host" element={
              <ProtectedRoute requiredRole="host">
                <HostDashboard />
              </ProtectedRoute>
            } />
            <Route path="host/properties/add" element={
              <ProtectedRoute requiredRole="host">
                <AddProperty />
              </ProtectedRoute>
            } />
            <Route path="host/properties" element={
              <ProtectedRoute requiredRole="host">
                <ManageProperties />
              </ProtectedRoute>
            } />
            <Route path="host/bookings" element={
              <ProtectedRoute requiredRole="host">
                <BookingManagement />
              </ProtectedRoute>
            } />

            {/* 404 Route */}
            <Route path="*" element={<NotFound />} />
          </Route>
        </Routes>
      </AnimatePresence>
    </div>
  )
}

export default App
