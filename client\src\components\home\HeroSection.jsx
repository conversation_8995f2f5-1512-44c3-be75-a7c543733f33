import { useState } from 'react'
import { motion } from 'framer-motion'
import { MagnifyingGlassIcon, MapPinIcon, CalendarDaysIcon, UserGroupIcon } from '@heroicons/react/24/outline'
import Button from '../ui/Button'
import Input from '../ui/Input'
import Card from '../ui/Card'

const HeroSection = () => {
  const [searchData, setSearchData] = useState({
    location: '',
    checkIn: '',
    checkOut: '',
    guests: 1
  })

  const handleSearch = (e) => {
    e.preventDefault()
    console.log('Search data:', searchData)
    // TODO: Implement search functionality
  }

  const handleInputChange = (field, value) => {
    setSearchData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  return (
    <section className="relative min-h-screen flex items-center justify-center overflow-hidden">
      {/* Background with gradient overlay */}
      <div className="absolute inset-0 bg-gradient-to-br from-primary-600 via-primary-700 to-secondary-800">
        <div className="absolute inset-0 bg-black/20"></div>
        {/* Animated background shapes */}
        <motion.div
          className="absolute top-20 left-20 w-72 h-72 bg-white/10 rounded-full blur-3xl"
          animate={{
            x: [0, 100, 0],
            y: [0, -50, 0],
          }}
          transition={{
            duration: 20,
            repeat: Infinity,
            ease: "linear"
          }}
        />
        <motion.div
          className="absolute bottom-20 right-20 w-96 h-96 bg-accent-400/20 rounded-full blur-3xl"
          animate={{
            x: [0, -80, 0],
            y: [0, 60, 0],
          }}
          transition={{
            duration: 25,
            repeat: Infinity,
            ease: "linear"
          }}
        />
      </div>

      {/* Content */}
      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          className="mb-12"
        >
          <h1 className="text-4xl md:text-6xl lg:text-7xl font-bold text-white mb-6">
            Find Your Perfect
            <span className="block bg-gradient-to-r from-accent-400 to-accent-300 bg-clip-text text-transparent">
              Stay
            </span>
          </h1>
          <p className="text-xl md:text-2xl text-white/90 max-w-3xl mx-auto leading-relaxed">
            Discover unique places to stay, from cozy apartments to luxury villas, 
            all around the world. Your next adventure starts here.
          </p>
        </motion.div>

        {/* Search Card */}
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.2 }}
          className="max-w-4xl mx-auto"
        >
          <Card 
            glass={true} 
            padding="lg" 
            rounded="2xl"
            className="backdrop-blur-xl bg-white/10 border border-white/20"
          >
            <form onSubmit={handleSearch} className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                {/* Location */}
                <div className="space-y-2">
                  <label className="block text-sm font-medium text-white/90">
                    Where
                  </label>
                  <div className="relative">
                    <Input
                      type="text"
                      placeholder="Search destinations"
                      value={searchData.location}
                      onChange={(e) => handleInputChange('location', e.target.value)}
                      leftIcon={<MapPinIcon className="w-5 h-5" />}
                      className="bg-white/90 border-white/30 text-gray-900 placeholder-gray-500"
                    />
                  </div>
                </div>

                {/* Check-in */}
                <div className="space-y-2">
                  <label className="block text-sm font-medium text-white/90">
                    Check in
                  </label>
                  <Input
                    type="date"
                    value={searchData.checkIn}
                    onChange={(e) => handleInputChange('checkIn', e.target.value)}
                    leftIcon={<CalendarDaysIcon className="w-5 h-5" />}
                    className="bg-white/90 border-white/30 text-gray-900"
                  />
                </div>

                {/* Check-out */}
                <div className="space-y-2">
                  <label className="block text-sm font-medium text-white/90">
                    Check out
                  </label>
                  <Input
                    type="date"
                    value={searchData.checkOut}
                    onChange={(e) => handleInputChange('checkOut', e.target.value)}
                    leftIcon={<CalendarDaysIcon className="w-5 h-5" />}
                    className="bg-white/90 border-white/30 text-gray-900"
                  />
                </div>

                {/* Guests */}
                <div className="space-y-2">
                  <label className="block text-sm font-medium text-white/90">
                    Guests
                  </label>
                  <div className="relative">
                    <select
                      value={searchData.guests}
                      onChange={(e) => handleInputChange('guests', parseInt(e.target.value))}
                      className="block w-full px-3 py-2.5 pl-10 bg-white/90 border border-white/30 rounded-lg text-gray-900 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                    >
                      {[1, 2, 3, 4, 5, 6, 7, 8].map(num => (
                        <option key={num} value={num}>
                          {num} {num === 1 ? 'Guest' : 'Guests'}
                        </option>
                      ))}
                    </select>
                    <UserGroupIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                  </div>
                </div>
              </div>

              {/* Search Button */}
              <div className="flex justify-center">
                <Button
                  type="submit"
                  size="lg"
                  variant="gradient"
                  leftIcon={<MagnifyingGlassIcon className="w-5 h-5" />}
                  className="px-12 py-4 text-lg font-semibold shadow-xl hover:shadow-2xl"
                >
                  Search
                </Button>
              </div>
            </form>
          </Card>
        </motion.div>

        {/* Stats */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.4 }}
          className="mt-16 grid grid-cols-2 md:grid-cols-4 gap-8 max-w-2xl mx-auto"
        >
          {[
            { label: 'Properties', value: '10K+' },
            { label: 'Happy Guests', value: '50K+' },
            { label: 'Cities', value: '100+' },
            { label: 'Countries', value: '25+' },
          ].map((stat, index) => (
            <motion.div
              key={stat.label}
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.5, delay: 0.6 + index * 0.1 }}
              className="text-center"
            >
              <div className="text-2xl md:text-3xl font-bold text-white mb-1">
                {stat.value}
              </div>
              <div className="text-sm text-white/80">
                {stat.label}
              </div>
            </motion.div>
          ))}
        </motion.div>
      </div>

      {/* Scroll indicator */}
      <motion.div
        className="absolute bottom-8 left-1/2 transform -translate-x-1/2"
        animate={{ y: [0, 10, 0] }}
        transition={{ duration: 2, repeat: Infinity }}
      >
        <div className="w-6 h-10 border-2 border-white/50 rounded-full flex justify-center">
          <div className="w-1 h-3 bg-white/70 rounded-full mt-2"></div>
        </div>
      </motion.div>
    </section>
  )
}

export default HeroSection
