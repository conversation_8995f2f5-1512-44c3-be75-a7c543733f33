import { motion } from 'framer-motion'
import { ArrowRightIcon, HomeIcon, UserGroupIcon, ShieldCheckIcon } from '@heroicons/react/24/outline'
import { useAuth } from '../../context/SupabaseAuthContext'
import { useNavigate } from 'react-router-dom'
import Button from '../ui/Button'
import Card from '../ui/Card'

const CallToAction = () => {
  const { isAuthenticated } = useAuth()
  const navigate = useNavigate()

  const features = [
    {
      icon: HomeIcon,
      title: 'List Your Property',
      description: 'Start earning by hosting guests in your space'
    },
    {
      icon: UserGroupIcon,
      title: 'Join Our Community',
      description: 'Connect with travelers from around the world'
    },
    {
      icon: ShieldCheckIcon,
      title: 'Secure & Trusted',
      description: 'Your safety and security are our top priority'
    }
  ]

  const handleGetStarted = () => {
    if (isAuthenticated) {
      navigate('/dashboard')
    } else {
      navigate('/register')
    }
  }

  const handleBecomeHost = () => {
    if (isAuthenticated) {
      navigate('/host/properties/add')
    } else {
      navigate('/register?role=host')
    }
  }

  return (
    <section className="py-16 bg-gradient-to-br from-primary-600 via-primary-700 to-secondary-800 relative overflow-hidden">
      {/* Background decorations */}
      <div className="absolute inset-0">
        <motion.div
          className="absolute top-20 left-20 w-64 h-64 bg-white/10 rounded-full blur-3xl"
          animate={{
            x: [0, 50, 0],
            y: [0, -30, 0],
          }}
          transition={{
            duration: 15,
            repeat: Infinity,
            ease: "linear"
          }}
        />
        <motion.div
          className="absolute bottom-20 right-20 w-80 h-80 bg-accent-400/20 rounded-full blur-3xl"
          animate={{
            x: [0, -40, 0],
            y: [0, 40, 0],
          }}
          transition={{
            duration: 20,
            repeat: Infinity,
            ease: "linear"
          }}
        />
      </div>

      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Main CTA Section */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8 }}
          className="text-center mb-16"
        >
          <h2 className="text-3xl md:text-5xl font-bold text-white mb-6">
            Ready to Start Your Journey?
          </h2>
          <p className="text-xl text-white/90 max-w-3xl mx-auto mb-8">
            Whether you're looking for your next adventure or want to share your space with travelers, 
            StayFinder is here to make it happen.
          </p>
          
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button
              size="lg"
              variant="secondary"
              onClick={handleGetStarted}
              rightIcon={<ArrowRightIcon className="w-5 h-5" />}
              className="px-8 py-4 text-lg font-semibold"
            >
              {isAuthenticated ? 'Go to Dashboard' : 'Get Started'}
            </Button>
            <Button
              size="lg"
              variant="outline"
              onClick={handleBecomeHost}
              className="px-8 py-4 text-lg font-semibold border-white text-white hover:bg-white hover:text-primary-600"
            >
              Become a Host
            </Button>
          </div>
        </motion.div>

        {/* Features Grid */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8, delay: 0.2 }}
          className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16"
        >
          {features.map((feature, index) => (
            <motion.div
              key={feature.title}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: 0.1 * index }}
            >
              <Card 
                glass={true}
                className="text-center p-6 backdrop-blur-xl bg-white/10 border border-white/20 hover:bg-white/20 transition-colors"
              >
                <div className="inline-flex items-center justify-center w-16 h-16 bg-white/20 rounded-full mb-4">
                  <feature.icon className="w-8 h-8 text-white" />
                </div>
                <h3 className="text-xl font-semibold text-white mb-2">
                  {feature.title}
                </h3>
                <p className="text-white/80">
                  {feature.description}
                </p>
              </Card>
            </motion.div>
          ))}
        </motion.div>

        {/* Newsletter Signup */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8, delay: 0.4 }}
        >
          <Card 
            glass={true}
            className="max-w-2xl mx-auto p-8 text-center backdrop-blur-xl bg-white/10 border border-white/20"
          >
            <h3 className="text-2xl font-bold text-white mb-4">
              Stay Updated
            </h3>
            <p className="text-white/90 mb-6">
              Get the latest deals, travel tips, and exclusive offers delivered to your inbox.
            </p>
            
            <form className="flex flex-col sm:flex-row gap-4 max-w-md mx-auto">
              <input
                type="email"
                placeholder="Enter your email"
                className="flex-1 px-4 py-3 rounded-lg bg-white/90 border border-white/30 text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-white focus:border-transparent"
              />
              <Button
                type="submit"
                variant="secondary"
                className="px-6 py-3 font-semibold whitespace-nowrap"
              >
                Subscribe
              </Button>
            </form>
            
            <p className="text-xs text-white/70 mt-4">
              We respect your privacy. Unsubscribe at any time.
            </p>
          </Card>
        </motion.div>

        {/* Stats */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8, delay: 0.6 }}
          className="mt-16 grid grid-cols-2 md:grid-cols-4 gap-8 text-center"
        >
          {[
            { label: 'Happy Guests', value: '50K+' },
            { label: 'Properties', value: '10K+' },
            { label: 'Cities', value: '100+' },
            { label: 'Countries', value: '25+' },
          ].map((stat, index) => (
            <motion.div
              key={stat.label}
              initial={{ opacity: 0, scale: 0.8 }}
              whileInView={{ opacity: 1, scale: 1 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: 0.8 + index * 0.1 }}
            >
              <div className="text-3xl md:text-4xl font-bold text-white mb-2">
                {stat.value}
              </div>
              <div className="text-white/80">
                {stat.label}
              </div>
            </motion.div>
          ))}
        </motion.div>
      </div>
    </section>
  )
}

export default CallToAction
