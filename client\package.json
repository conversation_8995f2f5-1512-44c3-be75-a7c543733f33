{"name": "stayfinder-client", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"@headlessui/react": "^1.7.17", "@heroicons/react": "^2.0.18", "@stripe/react-stripe-js": "^2.4.0", "@stripe/stripe-js": "^2.2.0", "@supabase/auth-ui-react": "^0.4.6", "@supabase/auth-ui-shared": "^0.1.8", "@supabase/supabase-js": "^2.38.5", "@tanstack/react-query": "^5.8.4", "clsx": "^2.0.0", "date-fns": "^2.30.0", "framer-motion": "^10.16.16", "js-cookie": "^3.0.5", "lucide-react": "^0.294.0", "mapbox-gl": "^2.15.0", "react": "^18.2.0", "react-datepicker": "^4.24.0", "react-dom": "^18.2.0", "react-helmet-async": "^1.3.0", "react-hook-form": "^7.48.2", "react-hot-toast": "^2.4.1", "react-infinite-scroll-component": "^6.1.0", "react-intersection-observer": "^9.5.3", "react-map-gl": "^7.1.7", "react-router-dom": "^6.20.1", "react-select": "^5.8.0", "swiper": "^11.0.5", "use-debounce": "^10.0.0"}, "devDependencies": {"@tailwindcss/aspect-ratio": "^0.4.2", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.16", "@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "@vitejs/plugin-react": "^4.1.1", "autoprefixer": "^10.4.16", "eslint": "^8.53.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.4", "postcss": "^8.4.32", "tailwindcss": "^3.3.6", "vite": "^4.5.0"}}