import { motion } from 'framer-motion'
import { Helmet } from 'react-helmet-async'
import { 
  CalendarDaysIcon, 
  HeartIcon, 
  MapPinIcon, 
  UserCircleIcon,
  PlusIcon,
  BuildingOfficeIcon
} from '@heroicons/react/24/outline'
import { useAuth } from '../context/SupabaseAuthContext'
import { Link } from 'react-router-dom'
import Card from '../components/ui/Card'
import Button from '../components/ui/Button'

const Dashboard = () => {
  const { user } = useAuth()

  const quickActions = [
    {
      title: 'Find a Stay',
      description: 'Search for your next perfect accommodation',
      icon: MapPinIcon,
      href: '/search',
      color: 'bg-blue-500'
    },
    {
      title: 'View Wishlist',
      description: 'See your saved properties',
      icon: HeartIcon,
      href: '/wishlist',
      color: 'bg-red-500'
    },
    {
      title: 'My Bookings',
      description: 'Manage your reservations',
      icon: CalendarDaysIcon,
      href: '/bookings',
      color: 'bg-green-500'
    },
    {
      title: 'Become a Host',
      description: 'Start hosting your property',
      icon: BuildingOfficeIcon,
      href: '/host/properties/add',
      color: 'bg-purple-500'
    }
  ]

  const recentBookings = [
    {
      id: 1,
      property: 'Modern Loft in Downtown',
      location: 'New York, NY',
      dates: 'Dec 15-18, 2024',
      status: 'upcoming',
      image: 'https://images.unsplash.com/photo-15227********-d24dbb6b0267?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80'
    },
    {
      id: 2,
      property: 'Cozy Beach House',
      location: 'Malibu, CA',
      dates: 'Nov 20-25, 2024',
      status: 'completed',
      image: 'https://images.unsplash.com/photo-*************-e29da59ef1c2?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80'
    }
  ]

  return (
    <>
      <Helmet>
        <title>Dashboard - StayFinder</title>
        <meta name="description" content="Manage your bookings, wishlist, and account settings." />
      </Helmet>

      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 pt-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* Welcome Section */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="mb-8"
          >
            <div className="flex items-center space-x-4 mb-6">
              <img
                src={user?.user_metadata?.avatar_url || `https://ui-avatars.com/api/?name=${user?.email}&background=6366f1&color=fff`}
                alt="Profile"
                className="w-16 h-16 rounded-full"
              />
              <div>
                <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
                  Welcome back, {user?.user_metadata?.first_name || user?.email?.split('@')[0]}!
                </h1>
                <p className="text-gray-600 dark:text-gray-400">
                  Ready for your next adventure?
                </p>
              </div>
            </div>
          </motion.div>

          {/* Quick Actions */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.1 }}
            className="mb-8"
          >
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
              Quick Actions
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              {quickActions.map((action, index) => (
                <motion.div
                  key={action.title}
                  initial={{ opacity: 0, scale: 0.9 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.5, delay: 0.1 * index }}
                >
                  <Link to={action.href}>
                    <Card hover={true} className="p-6 text-center group">
                      <div className={`w-12 h-12 ${action.color} rounded-lg flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform`}>
                        <action.icon className="w-6 h-6 text-white" />
                      </div>
                      <h3 className="font-semibold text-gray-900 dark:text-white mb-2">
                        {action.title}
                      </h3>
                      <p className="text-sm text-gray-600 dark:text-gray-400">
                        {action.description}
                      </p>
                    </Card>
                  </Link>
                </motion.div>
              ))}
            </div>
          </motion.div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Recent Bookings */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="lg:col-span-2"
            >
              <Card className="p-6">
                <div className="flex items-center justify-between mb-6">
                  <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
                    Recent Bookings
                  </h2>
                  <Link to="/bookings">
                    <Button variant="ghost" size="sm">
                      View all
                    </Button>
                  </Link>
                </div>

                {recentBookings.length > 0 ? (
                  <div className="space-y-4">
                    {recentBookings.map((booking) => (
                      <div
                        key={booking.id}
                        className="flex items-center space-x-4 p-4 border border-gray-200 dark:border-gray-700 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
                      >
                        <img
                          src={booking.image}
                          alt={booking.property}
                          className="w-16 h-16 rounded-lg object-cover"
                        />
                        <div className="flex-1">
                          <h3 className="font-semibold text-gray-900 dark:text-white">
                            {booking.property}
                          </h3>
                          <p className="text-sm text-gray-600 dark:text-gray-400">
                            {booking.location}
                          </p>
                          <p className="text-sm text-gray-500 dark:text-gray-500">
                            {booking.dates}
                          </p>
                        </div>
                        <div>
                          <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                            booking.status === 'upcoming' 
                              ? 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400'
                              : 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400'
                          }`}>
                            {booking.status}
                          </span>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <CalendarDaysIcon className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                    <p className="text-gray-500 dark:text-gray-400 mb-4">
                      No bookings yet
                    </p>
                    <Link to="/search">
                      <Button variant="primary">
                        Find your first stay
                      </Button>
                    </Link>
                  </div>
                )}
              </Card>
            </motion.div>

            {/* Profile & Settings */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.3 }}
              className="space-y-6"
            >
              {/* Profile Card */}
              <Card className="p-6">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                  Profile
                </h3>
                <div className="space-y-4">
                  <div className="flex items-center space-x-3">
                    <UserCircleIcon className="w-5 h-5 text-gray-400" />
                    <span className="text-sm text-gray-600 dark:text-gray-400">
                      {user?.user_metadata?.full_name || 'Complete your profile'}
                    </span>
                  </div>
                  <div className="flex items-center space-x-3">
                    <span className="w-5 h-5 text-gray-400">@</span>
                    <span className="text-sm text-gray-600 dark:text-gray-400">
                      {user?.email}
                    </span>
                  </div>
                </div>
                <Link to="/profile" className="block mt-4">
                  <Button variant="outline" size="sm" fullWidth>
                    Edit Profile
                  </Button>
                </Link>
              </Card>

              {/* Host Promotion */}
              <Card className="p-6 bg-gradient-to-br from-primary-500 to-secondary-500 text-white">
                <h3 className="text-lg font-semibold mb-2">
                  Become a Host
                </h3>
                <p className="text-sm text-white/90 mb-4">
                  Start earning by sharing your space with travelers from around the world.
                </p>
                <Link to="/host/properties/add">
                  <Button variant="secondary" size="sm" fullWidth>
                    Get Started
                  </Button>
                </Link>
              </Card>
            </motion.div>
          </div>
        </div>
      </div>
    </>
  )
}

export default Dashboard
