import { motion } from 'framer-motion'
import { Helmet } from 'react-helmet-async'
import HeroSection from '../components/home/<USER>'
import FeaturedProperties from '../components/home/<USER>'
import TopDestinations from '../components/home/<USER>'
import Testimonials from '../components/home/<USER>'
import CallToAction from '../components/home/<USER>'

const Home = () => {
  return (
    <>
      <Helmet>
        <title>StayFinder - Find Your Perfect Stay</title>
        <meta 
          name="description" 
          content="Discover unique places to stay around the world. From cozy apartments to luxury villas, find your perfect accommodation with StayFinder." 
        />
        <meta name="keywords" content="accommodation, hotels, vacation rentals, apartments, travel, booking" />
      </Helmet>

      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        transition={{ duration: 0.5 }}
      >
        {/* Hero Section */}
        <HeroSection />

        {/* Featured Properties */}
        <FeaturedProperties />

        {/* Top Destinations */}
        <TopDestinations />

        {/* Testimonials */}
        <Testimonials />

        {/* Call to Action */}
        <CallToAction />
      </motion.div>
    </>
  )
}

export default Home
