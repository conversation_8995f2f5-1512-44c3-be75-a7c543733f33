import { motion } from 'framer-motion'
import { Helmet } from 'react-helmet-async'

const PropertyDetail = () => {
  return (
    <>
      <Helmet>
        <title>Property Details - StayFinder</title>
        <meta name="description" content="View detailed information about this property." />
      </Helmet>

      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 pt-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center"
          >
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
              Property Details
            </h1>
            <p className="text-gray-600 dark:text-gray-400">
              This page is coming soon! Detailed property views will be implemented here.
            </p>
          </motion.div>
        </div>
      </div>
    </>
  )
}

export default PropertyDetail
