# 🏠 StayFinder - Modern Airbnb Clone

A modern, responsive, and feature-rich rental property platform built with React.js, Node.js, Express.js, and Supabase. StayFinder offers an enhanced, clean, and visually striking UI/UX that's more polished and user-friendly than traditional booking platforms.

![StayFinder Preview](https://via.placeholder.com/1200x600/6366f1/ffffff?text=StayFinder+Preview)

## ✨ Features

### 🎨 Modern UI/UX
- **Enhanced Design**: Clean, minimal yet visually striking interface
- **Dark Mode**: System preference detection with manual toggle
- **Responsive**: Mobile-first design (mobile, tablet, desktop, widescreen)
- **Animations**: Smooth Framer Motion transitions and micro-interactions
- **Neumorphic Cards**: Modern card designs with subtle shadows
- **Gradient Headers**: Beautiful gradient backgrounds and CTAs

### 🔐 Authentication & User Management
- **Supabase Auth**: Email/password and social login (Google, GitHub)
- **Role-based Access**: Guest, Host, and Admin roles
- **Protected Routes**: Secure route protection
- **Profile Management**: Complete user profile system

### 🏡 Property Management
- **Property Listings**: Beautiful card layouts with advanced filtering
- **Property Details**: Comprehensive property pages with image carousels
- **Host Dashboard**: Complete property management for hosts
- **Add/Edit Listings**: Rich property creation and editing forms
- **Image Upload**: Supabase storage integration for property images

### 📅 Booking System
- **Advanced Search**: Location, dates, guests with autocomplete
- **Availability Calendar**: Real-time availability checking
- **Booking Flow**: Secure checkout process (ready for Stripe integration)
- **Booking Management**: Complete booking history and management

### 💝 Additional Features
- **Wishlist/Favorites**: Save and manage favorite properties
- **Reviews & Ratings**: Property and host review system
- **Real-time Chat**: Host-guest communication (Supabase Realtime)
- **Top Destinations**: Featured destinations carousel
- **Testimonials**: Customer testimonials section

## 🛠 Tech Stack

### Frontend
- **React.js 18** - Modern React with hooks
- **Vite** - Fast build tool and dev server
- **Tailwind CSS** - Utility-first CSS framework
- **Framer Motion** - Smooth animations and transitions
- **React Router** - Client-side routing
- **React Query** - Server state management
- **React Hook Form** - Form handling
- **React Hot Toast** - Beautiful notifications

### Backend & Database
- **Supabase** - Backend-as-a-Service
  - PostgreSQL database
  - Authentication
  - Real-time subscriptions
  - Storage for images
  - Row Level Security (RLS)

### Additional Tools
- **Heroicons** - Beautiful SVG icons
- **React Helmet Async** - SEO and meta tags
- **Clsx** - Conditional CSS classes
- **Date-fns** - Date manipulation

## 🚀 Getting Started

### Prerequisites
- Node.js 18+ and npm
- Supabase account
- Git

### 1. Clone the Repository
```bash
git clone https://github.com/yourusername/stayfinder.git
cd stayfinder
```

### 2. Install Dependencies
```bash
# Install root dependencies
npm run install-deps

# Or install manually
npm install
cd client && npm install
cd ../server && npm install
```

### 3. Set Up Supabase

1. Create a new project at [supabase.com](https://supabase.com)
2. Go to Settings > API to get your project URL and anon key
3. Run the SQL schema from `client/docs/supabase-schema.sql` in the Supabase SQL editor
4. Set up authentication providers (Google, GitHub) in Authentication > Providers

### 4. Environment Variables

Create environment files:

**Client (.env in client directory):**
```env
VITE_SUPABASE_URL=your_supabase_project_url
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key
VITE_MAPBOX_TOKEN=your_mapbox_access_token
VITE_STRIPE_PUBLISHABLE_KEY=your_stripe_publishable_key
VITE_APP_NAME=StayFinder
VITE_APP_URL=http://localhost:5173
```

**Server (.env in server directory):**
```env
# Database
SUPABASE_URL=your_supabase_project_url
SUPABASE_SERVICE_KEY=your_supabase_service_role_key

# Server
PORT=5000
NODE_ENV=development
CLIENT_URL=http://localhost:5173

# Optional: For additional integrations
STRIPE_SECRET_KEY=your_stripe_secret_key
MAPBOX_SECRET_TOKEN=your_mapbox_secret_token
```

### 5. Run the Application

```bash
# Development mode (runs both client and server)
npm run dev

# Or run separately
npm run client  # Frontend only
npm run server  # Backend only
```

The application will be available at:
- Frontend: http://localhost:5173
- Backend: http://localhost:5000

## 📁 Project Structure

```
stayfinder/
├── client/                 # React frontend
│   ├── src/
│   │   ├── components/     # Reusable components
│   │   │   ├── ui/         # UI components (Button, Input, Card, etc.)
│   │   │   ├── layout/     # Layout components (Navbar, Footer)
│   │   │   └── home/       # Home page components
│   │   ├── pages/          # Page components
│   │   │   ├── auth/       # Authentication pages
│   │   │   └── host/       # Host-specific pages
│   │   ├── context/        # React contexts (Auth, Theme)
│   │   ├── lib/            # Utilities and configurations
│   │   └── services/       # API services
│   ├── docs/               # Documentation
│   └── public/             # Static assets
├── server/                 # Express.js backend (optional)
└── package.json           # Root package.json
```

## 🎨 UI Components

### Core Components
- **Button**: Multiple variants with animations
- **Input**: Enhanced inputs with icons and validation
- **Card**: Neumorphic cards with hover effects
- **LoadingSpinner**: Animated loading states

### Layout Components
- **Navbar**: Responsive navigation with dark mode toggle
- **Footer**: Comprehensive footer with links and newsletter
- **HeroSection**: Animated hero with search functionality

### Page Components
- **Home**: Complete landing page with all sections
- **Dashboard**: User dashboard with quick actions
- **Auth Pages**: Login, Register, Forgot Password

## 🔧 Customization

### Tailwind Configuration
The project uses an extended Tailwind configuration with:
- Custom color palette
- Additional animations
- Neumorphic shadows
- Extended spacing and typography

### Theme System
- Dark mode support with system preference detection
- Custom CSS variables for consistent theming
- Framer Motion animations throughout

## 🚀 Deployment

### Frontend (Vercel/Netlify)
1. Build the client: `cd client && npm run build`
2. Deploy the `client/dist` folder
3. Set environment variables in your hosting platform

### Backend (Railway/Render/Heroku)
1. Deploy the `server` directory
2. Set environment variables
3. Ensure Supabase connection

### Database
- Supabase handles hosting automatically
- Ensure RLS policies are properly configured
- Set up storage buckets for images

## 📚 API Documentation

The application primarily uses Supabase's auto-generated APIs:
- **Authentication**: Supabase Auth API
- **Database**: Supabase REST API with RLS
- **Storage**: Supabase Storage API
- **Real-time**: Supabase Realtime subscriptions

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/amazing-feature`
3. Commit your changes: `git commit -m 'Add amazing feature'`
4. Push to the branch: `git push origin feature/amazing-feature`
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- [Supabase](https://supabase.com) for the amazing backend platform
- [Tailwind CSS](https://tailwindcss.com) for the utility-first CSS framework
- [Framer Motion](https://framer.com/motion) for beautiful animations
- [Heroicons](https://heroicons.com) for the icon set
- [Unsplash](https://unsplash.com) for the beautiful placeholder images

## 📞 Support

If you have any questions or need help, please:
1. Check the [documentation](./docs)
2. Open an issue on GitHub
3. Contact the development team

---

**Built with ❤️ by the StayFinder Team**
