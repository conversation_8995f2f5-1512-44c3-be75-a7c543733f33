import { motion } from 'framer-motion'
import { StarIcon, HeartIcon, MapPinIcon } from '@heroicons/react/24/solid'
import { HeartIcon as HeartOutlineIcon } from '@heroicons/react/24/outline'
import { useState } from 'react'
import Card from '../ui/Card'
import Button from '../ui/Button'

const FeaturedProperties = () => {
  const [favorites, setFavorites] = useState(new Set())

  const toggleFavorite = (id) => {
    setFavorites(prev => {
      const newFavorites = new Set(prev)
      if (newFavorites.has(id)) {
        newFavorites.delete(id)
      } else {
        newFavorites.add(id)
      }
      return newFavorites
    })
  }

  // Mock data - replace with real data from Supabase
  const featuredProperties = [
    {
      id: 1,
      title: 'Modern Loft in Downtown',
      location: 'New York, NY',
      price: 120,
      rating: 4.8,
      reviews: 124,
      image: 'https://images.unsplash.com/photo-1522708323590-d24dbb6b0267?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2340&q=80',
      type: 'Apartment',
      guests: 4,
      bedrooms: 2,
      bathrooms: 2
    },
    {
      id: 2,
      title: 'Cozy Beach House',
      location: 'Malibu, CA',
      price: 280,
      rating: 4.9,
      reviews: 89,
      image: 'https://images.unsplash.com/photo-1499793983690-e29da59ef1c2?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2340&q=80',
      type: 'House',
      guests: 8,
      bedrooms: 4,
      bathrooms: 3
    },
    {
      id: 3,
      title: 'Luxury Villa with Pool',
      location: 'Miami, FL',
      price: 450,
      rating: 5.0,
      reviews: 67,
      image: 'https://images.unsplash.com/photo-1613490493576-7fde63acd811?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2071&q=80',
      type: 'Villa',
      guests: 10,
      bedrooms: 5,
      bathrooms: 4
    },
    {
      id: 4,
      title: 'Mountain Cabin Retreat',
      location: 'Aspen, CO',
      price: 200,
      rating: 4.7,
      reviews: 156,
      image: 'https://images.unsplash.com/photo-1449824913935-59a10b8d2000?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2340&q=80',
      type: 'Cabin',
      guests: 6,
      bedrooms: 3,
      bathrooms: 2
    }
  ]

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  }

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.5
      }
    }
  }

  return (
    <section className="py-16 bg-white dark:bg-gray-900">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
          className="text-center mb-12"
        >
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
            Featured Properties
          </h2>
          <p className="text-lg text-gray-600 dark:text-gray-400 max-w-2xl mx-auto">
            Discover our handpicked selection of exceptional stays, each offering unique experiences and outstanding hospitality.
          </p>
        </motion.div>

        <motion.div
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6"
        >
          {featuredProperties.map((property) => (
            <motion.div key={property.id} variants={itemVariants}>
              <Card 
                hover={true}
                className="group cursor-pointer overflow-hidden"
                onClick={() => console.log('Navigate to property', property.id)}
              >
                <div className="relative">
                  <img
                    src={property.image}
                    alt={property.title}
                    className="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"
                  />
                  
                  {/* Favorite button */}
                  <button
                    onClick={(e) => {
                      e.stopPropagation()
                      toggleFavorite(property.id)
                    }}
                    className="absolute top-3 right-3 p-2 rounded-full bg-white/80 hover:bg-white transition-colors"
                  >
                    {favorites.has(property.id) ? (
                      <HeartIcon className="w-5 h-5 text-red-500" />
                    ) : (
                      <HeartOutlineIcon className="w-5 h-5 text-gray-600" />
                    )}
                  </button>

                  {/* Property type badge */}
                  <div className="absolute top-3 left-3">
                    <span className="px-2 py-1 bg-primary-500 text-white text-xs font-medium rounded-full">
                      {property.type}
                    </span>
                  </div>
                </div>

                <div className="p-4">
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center space-x-1">
                      <StarIcon className="w-4 h-4 text-yellow-400" />
                      <span className="text-sm font-medium text-gray-900 dark:text-white">
                        {property.rating}
                      </span>
                      <span className="text-sm text-gray-500 dark:text-gray-400">
                        ({property.reviews})
                      </span>
                    </div>
                    <div className="text-right">
                      <span className="text-lg font-bold text-gray-900 dark:text-white">
                        ${property.price}
                      </span>
                      <span className="text-sm text-gray-500 dark:text-gray-400">/night</span>
                    </div>
                  </div>

                  <h3 className="font-semibold text-gray-900 dark:text-white mb-1 group-hover:text-primary-600 dark:group-hover:text-primary-400 transition-colors">
                    {property.title}
                  </h3>

                  <div className="flex items-center text-sm text-gray-500 dark:text-gray-400 mb-3">
                    <MapPinIcon className="w-4 h-4 mr-1" />
                    {property.location}
                  </div>

                  <div className="flex items-center justify-between text-sm text-gray-500 dark:text-gray-400">
                    <span>{property.guests} guests</span>
                    <span>{property.bedrooms} beds</span>
                    <span>{property.bathrooms} baths</span>
                  </div>
                </div>
              </Card>
            </motion.div>
          ))}
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6, delay: 0.3 }}
          className="text-center mt-12"
        >
          <Button
            variant="outline"
            size="lg"
            onClick={() => console.log('View all properties')}
          >
            View All Properties
          </Button>
        </motion.div>
      </div>
    </section>
  )
}

export default FeaturedProperties
