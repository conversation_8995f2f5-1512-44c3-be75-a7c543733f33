import { Link } from 'react-router-dom'
import { motion } from 'framer-motion'
import { Helmet } from 'react-helmet-async'
import { HomeIcon, MagnifyingGlassIcon } from '@heroicons/react/24/outline'
import But<PERSON> from '../components/ui/Button'

const NotFound = () => {
  return (
    <>
      <Helmet>
        <title>Page Not Found - StayFinder</title>
        <meta name="description" content="The page you're looking for doesn't exist." />
      </Helmet>

      <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900 px-4">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="text-center max-w-md mx-auto"
        >
          <motion.div
            initial={{ scale: 0.8 }}
            animate={{ scale: 1 }}
            transition={{ duration: 0.5, delay: 0.2 }}
            className="mb-8"
          >
            <div className="text-9xl font-bold text-primary-500 mb-4">404</div>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
              Page Not Found
            </h1>
            <p className="text-gray-600 dark:text-gray-400 mb-8">
              Oops! The page you're looking for doesn't exist. It might have been moved, deleted, or you entered the wrong URL.
            </p>
          </motion.div>

          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.5, delay: 0.4 }}
            className="space-y-4"
          >
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link to="/">
                <Button
                  variant="primary"
                  leftIcon={<HomeIcon className="w-5 h-5" />}
                >
                  Go Home
                </Button>
              </Link>
              <Link to="/search">
                <Button
                  variant="outline"
                  leftIcon={<MagnifyingGlassIcon className="w-5 h-5" />}
                >
                  Search Properties
                </Button>
              </Link>
            </div>
          </motion.div>
        </motion.div>
      </div>
    </>
  )
}

export default NotFound
